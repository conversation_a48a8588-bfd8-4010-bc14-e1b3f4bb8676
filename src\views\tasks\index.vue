<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { showToast } from '@/utils/toast'
import { getTasks, type GetTasksParams } from '@/api/tasks'
import { getMediaList, type MediaPlatform } from '@/api/common'
import { useTaskStore } from '@/stores/tasks'

// 路由实例
const router = useRouter()

// 任务状态
const taskStore = useTaskStore()

// 筛选参数
const filterParams = ref<GetTasksParams>({
  media_id: '',
  rwtype_id: ''
})

// 显示筛选弹窗
const showFilterPopup = ref(false)

// 下拉刷新状态
const refreshing = ref(false)

// 媒体平台选项
const mediaOptions = ref<Array<{ text: string; value: string }>>([
  { text: '全部平台', value: '' }
])

// 原始媒体平台数据
const mediaList = ref<MediaPlatform[]>([])

// 获取媒体平台列表
const fetchMediaList = async () => {
  try {
    const list = await getMediaList()
    mediaList.value = list
    
    // 转换为选项格式
    const options = [
      { text: '全部平台', value: '' },
      ...list.map(item => ({
        text: item.name,
        value: item.id.toString()
      }))
    ]
    mediaOptions.value = options
  } catch (error) {
    console.error('获取媒体平台列表失败:', error)
    // 使用默认选项
    mediaOptions.value = [
      { text: '全部平台', value: '' },
      { text: '小红书', value: '15' },
      { text: '抖音', value: '10' },
      { text: '微博号', value: '13' },
      { text: '快手', value: '14' }
    ]
  }
}

// 任务类型选项（示例数据，可根据实际需求调整）
const typeOptions = [
  { text: '全部类型', value: '' },
  { text: '普通任务', value: '1' },
  { text: '评论任务', value: '2' },
  { text: '点赞任务', value: '3' },
  { text: '举报任务', value: '4' }
]

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, 'default' | 'primary' | 'success' | 'warning' | 'danger'> = {
    'pending': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'processing': 'primary',
    'reviewing': 'default'
  }
  return typeMap[status] || 'default'
}

// 格式化时间戳为可读格式
const formatTime = (timestamp: number | undefined) => {
  if (!timestamp) return '未设置'
  const date = new Date(timestamp * 1000)
  const now = new Date()
  const isToday = date.toDateString() === now.toDateString()
  
  if (isToday) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}

// 计算剩余时间
const formatRemainingTime = (endTimeStamp: number | undefined) => {
  if (!endTimeStamp) return '无限期'
  
  const endTime = new Date(endTimeStamp * 1000)
  const now = new Date()
  const diffMs = endTime.getTime() - now.getTime()
  
  // 如果已过期
  if (diffMs <= 0) {
    return '已过期'
  }
  
  // 计算剩余天数、小时、分钟
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  const remainingHours = diffHours % 24
  const remainingMinutes = diffMinutes % 60
  
  // 根据剩余时间长短返回不同格式
  if (diffDays > 0) {
    if (remainingHours > 0) {
      return `剩余${diffDays}天${remainingHours}小时`
    } else {
      return `剩余${diffDays}天`
    }
  } else if (diffHours > 0) {
    if (remainingMinutes > 0) {
      return `剩余${diffHours}小时${remainingMinutes}分钟`
    } else {
      return `剩余${diffHours}小时`
    }
  } else {
    return `剩余${diffMinutes}分钟`
  }
}

// 获取剩余时间的状态类名
const getRemainingTimeClass = (endTimeStamp: number | undefined) => {
  if (!endTimeStamp) return 'time-unlimited'
  
  const endTime = new Date(endTimeStamp * 1000)
  const now = new Date()
  const diffMs = endTime.getTime() - now.getTime()
  
  if (diffMs <= 0) {
    return 'time-expired'
  } else if (diffMs <= 24 * 60 * 60 * 1000) {
    // 24小时内
    return 'time-urgent'
  } else if (diffMs <= 3 * 24 * 60 * 60 * 1000) {
    // 3天内
    return 'time-warning'
  } else {
    return 'time-normal'
  }
}

// 获取任务列表（首次加载或刷新）
const fetchTasks = async (params?: GetTasksParams, showLoading = true) => {
  if (showLoading) {
    taskStore.setLoading(true)
  }
  
  try {
    // 重置分页状态
    taskStore.resetPagination()
    
    // 使用筛选参数或默认参数，重置为第一页
    const queryParams = {
      ...(params || filterParams.value),
      page: 1,
      pageSize: taskStore.pageSize
    }
    
    console.log('首次请求任务列表，参数:', queryParams)
    
    const result = await getTasks(queryParams)
    
    console.log('首次请求任务列表响应:', {
      tasks: result.tasks.length,
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })
    
    // 设置任务列表和分页信息
    taskStore.setTaskList(result.tasks)
    taskStore.setPaginationInfo({
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })
    
  } catch (error: any) {
    console.error('获取任务列表失败:', error)
    // 业务代码控制错误提示
    const errorMessage = error.message || '获取任务列表失败'
    showToast(errorMessage)
  } finally {
    if (showLoading) {
      taskStore.setLoading(false)
    }
    // 确保加载更多状态被重置
    console.log('fetchTasks finally: 重置 isLoadingMore 为 false')
    taskStore.setLoadingMore(false)
    
    // 短暂延迟后再次检查状态
    setTimeout(() => {
      console.log('fetchTasks 完成后状态检查:', {
        isLoadingMore: taskStore.isLoadingMore,
        hasMore: taskStore.hasMore,
        currentPage: taskStore.currentPage
      })
    }, 100)
  }
}

// 加载更多任务
const loadMoreTasks = async () => {
  console.log('loadMoreTasks 开始，检查状态:', {
    isLoadingMore: taskStore.isLoadingMore,
    hasMore: taskStore.hasMore,
    currentPage: taskStore.currentPage
  })
  
  if (!taskStore.hasMore) {
    console.log('取消加载更多：没有更多数据')
    return
  }
  
  if (taskStore.isLoadingMore) {
    console.log('取消加载更多：正在加载中')
    return
  }
  
  console.log('开始设置 isLoadingMore = true')
  taskStore.setLoadingMore(true)
  
  try {
    const queryParams = {
      ...filterParams.value,
      page: taskStore.currentPage + 1,
      pageSize: taskStore.pageSize
    }
    
    console.log('加载更多任务，参数:', queryParams)
    
    const result = await getTasks(queryParams)
    
    console.log('加载更多任务响应:', {
      newTasks: result.tasks.length,
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })
    
    // 追加任务列表并更新分页信息
    taskStore.appendTaskList(result.tasks)
    taskStore.setPaginationInfo({
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })
    
  } catch (error: any) {
    console.error('加载更多任务失败:', error)
    const errorMessage = error.message || '加载更多失败'
    showToast(errorMessage)
  } finally {
    console.log('loadMoreTasks finally: 设置 isLoadingMore = false')
    taskStore.setLoadingMore(false)
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    await fetchTasks(filterParams.value, false)
  } finally {
    refreshing.value = false
  }
}

// 上拉加载更多
const onLoad = async () => {
  console.log('onLoad 触发，当前状态:', {
    isLoadingMore: taskStore.isLoadingMore,
    hasMore: taskStore.hasMore,
    currentPage: taskStore.currentPage,
    total: taskStore.total,
    taskListLength: taskStore.taskList.length
  })
  
  // 如果已经在加载中，直接返回
  if (taskStore.isLoadingMore) {
    console.log('onLoad: 已在加载中，跳过')
    return
  }
  
  await loadMoreTasks()
}

// 应用筛选
const applyFilter = () => {
  showFilterPopup.value = false
  fetchTasks(filterParams.value)
}

// 重置筛选
const resetFilter = () => {
  filterParams.value = {
    media_id: '',
    rwtype_id: ''
  }
  fetchTasks(filterParams.value)
}

// 跳转到任务详情
const goToTaskDetail = (taskId: string) => {
  router.push(`/task-detail/${taskId}?from=tasks`)
}

// 页面加载时获取数据
onMounted(async () => {
  // 并行获取任务列表和媒体平台列表
  await Promise.all([
    fetchTasks(),
    fetchMediaList()
  ])
})
</script>

<template>
  <div class="tasks-container">
    <!-- 导航栏 -->
    <van-nav-bar title="任务大厅" fixed placeholder></van-nav-bar>
    
    <!-- 筛选工具栏 -->
    <div class="filter-bar">
      <div class="filter-info">
        <span v-if="filterParams.media_id || filterParams.rwtype_id" class="active-filter">
          筛选中
        </span>
        <span v-else class="no-filter">全部任务</span>
      </div>
      <van-button 
        type="primary" 
        size="mini" 
        @click="showFilterPopup = true"
      >
        筛选
      </van-button>
    </div>
    
    <!-- 任务列表 -->
    <div class="tasks-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          :loading="taskStore.isLoadingMore"
          :finished="!taskStore.hasMore"
          finished-text="没有更多了"
          loading-text="加载中..."
          :offset="50"
          @load="onLoad"
          @update:loading="(val) => console.log('van-list update:loading:', val)"
        >
          <div
            v-for="task in taskStore.taskList"
            :key="task.id"
            class="task-card"
            @click="goToTaskDetail(task.id)"
          >
            <!-- 书签标签 -->
            <div class="bookmark-tag">
              <div class="bookmark-inner">
                <span class="tag-text">进行中</span>
              </div>
            </div>
            
            <div class="task-content">
              <div class="task-header">
                <div class="task-info">
                  <h3 class="task-title">{{ task.title }}</h3>
                </div>
              </div>
              
              <div class="task-meta">
                <div class="meta-row">
                  <div class="publish-date">
                    <van-icon name="clock-o" size="12" />
                    <span>{{ formatTime(task.starttime) }}</span>
                  </div>
                  <div class="remaining-time-wrapper">
                    <span class="time-value remaining-time" :class="getRemainingTimeClass(task.endtime)">{{ formatRemainingTime(task.endtime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="taskStore.taskList.length === 0 && !taskStore.isLoading" class="empty-state">
            <van-empty 
              description="暂无任务"
              image="search"
            />
          </div>
          
          <!-- 首次加载loading -->
          <div v-if="taskStore.isLoading && taskStore.taskList.length === 0" class="loading-state">
            <van-loading size="24px" vertical>加载中...</van-loading>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    
    <!-- 筛选弹窗 -->
    <van-popup 
      v-model:show="showFilterPopup" 
      position="bottom" 
      :style="{ height: '60%' }"
      round
    >
      <div class="filter-popup">
        <div class="filter-header">
          <h3>筛选任务</h3>
          <van-button 
            type="default" 
            size="small" 
            @click="resetFilter"
          >
            重置
          </van-button>
        </div>
        
        <div class="filter-content">
          <div class="filter-section">
            <h4>媒体平台</h4>
            <van-radio-group v-model="filterParams.media_id">
              <van-radio 
                v-for="option in mediaOptions" 
                :key="option.value"
                :name="option.value"
              >
                {{ option.text }}
              </van-radio>
            </van-radio-group>
          </div>
          
          <div class="filter-section">
            <h4>任务类型</h4>
            <van-radio-group v-model="filterParams.rwtype_id">
              <van-radio 
                v-for="option in typeOptions" 
                :key="option.value"
                :name="option.value"
              >
                {{ option.text }}
              </van-radio>
            </van-radio-group>
          </div>
        </div>
        
        <div class="filter-footer">
          <van-button 
            type="primary" 
            block 
            @click="applyFilter"
          >
            确定筛选
          </van-button>
        </div>
      </div>
    </van-popup>
    
    <!-- 底部导航 -->
    <van-tabbar route fixed>
      <van-tabbar-item icon="apps-o" to="/tasks">任务</van-tabbar-item>
      <van-tabbar-item icon="completed" to="/my-tasks">已接任务</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/user">个人中心</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped>
.tasks-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 50px;
}

/* 筛选工具栏 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.filter-info .active-filter {
  color: #ff6b35;
  font-size: 14px;
  font-weight: 500;
}

.filter-info .no-filter {
  color: #969799;
  font-size: 14px;
}

.tasks-list {
  padding: 8px 16px 20px;
}

.task-card {
  position: relative;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 书签标签样式 */
.bookmark-tag {
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 35px;
  z-index: 2;
}

.bookmark-inner {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8a50 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.tag-text {
  font-size: 11px;
  color: #fff;
  font-weight: 600;
  transform: translateX(-4px) translateY(-2px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.task-content {
  padding: 16px;
  padding-top: 20px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 14px;
}

.task-info {
  flex: 1;
  margin-right: 12px;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 6px 0;
  line-height: 1.4;
}

.task-desc {
  font-size: 13px;
  color: #969799;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-meta {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.meta-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.publish-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #969799;
}

.remaining-time-wrapper {
  display: flex;
  align-items: center;
}

.time-value {
  font-size: 13px;
  font-weight: 500;
  color: #323233;
}

.remaining-time {
  font-weight: 600;
}

/* 剩余时间状态颜色 */
.time-expired {
  color: #ee0a24 !important;
}

.time-urgent {
  color: #ff976a !important;
}

.time-warning {
  color: #ff6b35 !important;
}

.time-normal {
  color: #07c160 !important;
}

.time-unlimited {
  color: #969799 !important;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.loading-state {
  padding: 60px 0;
  text-align: center;
}

.data-info {
  padding: 12px 16px;
  text-align: center;
  background: #f7f8fa;
}

.data-text {
  font-size: 12px;
  color: #969799;
}

.van-pull-refresh {
  min-height: calc(100vh - 180px);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .task-title {
    font-size: 15px;
  }
  
  .publish-date {
    font-size: 11px;
  }
  
  .time-value {
    font-size: 11px;
  }
  
  .bookmark-tag {
    width: 70px;
    height: 32px;
  }
  
  .tag-text {
    font-size: 10px;
  }
}

/* 筛选弹窗样式 */
.filter-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.filter-section .van-radio {
  margin-bottom: 8px;
}

.filter-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style> 